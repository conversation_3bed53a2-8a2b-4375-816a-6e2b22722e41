import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from modules.auth.application.oauth_client_service import OAuthClientService
from modules.auth.domain.models import OAuthProvider


@pytest.fixture
def oauth_client_service():
    """Create OAuthClientService instance for testing."""
    return OAuthClientService(
        github_client_id="test_github_client_id",
        github_client_secret="test_github_client_secret",
        google_client_id="test_google_client_id",
        google_client_secret="test_google_client_secret",
        redirect_uri="http://localhost:8000/api/v1/auth/oauth/callback",
    )


class TestOAuthClientService:
    """Test cases for OAuthClientService."""

    def should_generate_github_authorization_url_when_valid_provider_given(
        self, oauth_client_service: OAuthClientService
    ) -> None:
        """Test that GitHub authorization URL is generated correctly."""
        url = oauth_client_service.generate_authorization_url(OAuthProvider.GITHUB)
        
        assert "https://github.com/login/oauth/authorize" in url
        assert "client_id=test_github_client_id" in url
        assert "redirect_uri=http%3A%2F%2Flocalhost%3A8000%2Fapi%2Fv1%2Fauth%2Foauth%2Fcallback" in url
        assert "scope=user%3Aemail" in url
        assert "response_type=code" in url

    def should_generate_google_authorization_url_when_valid_provider_given(
        self, oauth_client_service: OAuthClientService
    ) -> None:
        """Test that Google authorization URL is generated correctly."""
        url = oauth_client_service.generate_authorization_url(OAuthProvider.GOOGLE)
        
        assert "https://accounts.google.com/o/oauth2/v2/auth" in url
        assert "client_id=test_google_client_id" in url
        assert "redirect_uri=http%3A%2F%2Flocalhost%3A8000%2Fapi%2Fv1%2Fauth%2Foauth%2Fcallback" in url
        assert "scope=openid+email+profile" in url
        assert "response_type=code" in url

    def should_include_state_parameter_when_state_provided(
        self, oauth_client_service: OAuthClientService
    ) -> None:
        """Test that state parameter is included when provided."""
        state = "test_state_123"
        url = oauth_client_service.generate_authorization_url(OAuthProvider.GITHUB, state)
        
        assert f"state={state}" in url

    def should_raise_error_when_unsupported_provider_given(
        self, oauth_client_service: OAuthClientService
    ) -> None:
        """Test that error is raised for unsupported provider."""
        with pytest.raises(ValueError, match="Unsupported OAuth provider"):
            oauth_client_service.generate_authorization_url("unsupported")

    @pytest.mark.asyncio
    async def should_exchange_code_for_token_when_valid_github_code_given(
        self, oauth_client_service: OAuthClientService
    ) -> None:
        """Test that authorization code is exchanged for token successfully."""
        mock_token = {"access_token": "test_token", "token_type": "bearer"}
        
        with patch("modules.auth.application.oauth_client_service.AsyncOAuth2Client") as mock_oauth_client:
            mock_client_instance = MagicMock()
            mock_client_instance.fetch_token = AsyncMock(return_value=mock_token)
            mock_oauth_client.return_value = mock_client_instance

            result = await oauth_client_service.exchange_code_for_token(
                OAuthProvider.GITHUB, "test_code"
            )

            assert result == mock_token
            mock_client_instance.fetch_token.assert_called_once()

    @pytest.mark.asyncio
    async def should_get_github_user_info_when_valid_token_given(
        self, oauth_client_service: OAuthClientService
    ) -> None:
        """Test that GitHub user info is fetched successfully."""
        mock_github_response = {
            "id": 12345,
            "login": "testuser",
            "email": "<EMAIL>",
            "name": "Test User",
            "avatar_url": "https://avatar.url",
        }
        
        with patch("httpx.AsyncClient") as mock_client:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_github_response
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            result = await oauth_client_service.get_user_info(
                OAuthProvider.GITHUB, "test_token"
            )
            
            expected = {
                "oauth_id": "12345",
                "email": "<EMAIL>",
                "name": "Test User",
                "username": "testuser",
                "avatar_url": "https://avatar.url",
            }
            assert result == expected

    @pytest.mark.asyncio
    async def should_get_google_user_info_when_valid_token_given(
        self, oauth_client_service: OAuthClientService
    ) -> None:
        """Test that Google user info is fetched successfully."""
        mock_google_response = {
            "id": "67890",
            "email": "<EMAIL>",
            "name": "Test User",
            "picture": "https://picture.url",
        }
        
        with patch("httpx.AsyncClient") as mock_client:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_google_response
            mock_response.raise_for_status.return_value = None
            
            mock_client_instance = AsyncMock()
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance
            
            result = await oauth_client_service.get_user_info(
                OAuthProvider.GOOGLE, "test_token"
            )
            
            expected = {
                "oauth_id": "67890",
                "email": "<EMAIL>",
                "name": "Test User",
                "username": "test",
                "avatar_url": "https://picture.url",
            }
            assert result == expected

    @pytest.mark.asyncio
    async def should_handle_complete_oauth_flow_when_valid_code_given(
        self, oauth_client_service: OAuthClientService
    ) -> None:
        """Test that complete OAuth flow works correctly."""
        mock_token = {"access_token": "test_token", "token_type": "bearer"}
        mock_user_info = {
            "oauth_id": "12345",
            "email": "<EMAIL>",
            "name": "Test User",
            "username": "testuser",
            "avatar_url": "https://avatar.url",
        }
        
        with patch.object(
            oauth_client_service, "exchange_code_for_token", return_value=mock_token
        ) as mock_exchange:
            with patch.object(
                oauth_client_service, "get_user_info", return_value=mock_user_info
            ) as mock_get_user:
                result = await oauth_client_service.handle_oauth_flow(
                    OAuthProvider.GITHUB, "test_code"
                )
                
                assert result["user_info"] == mock_user_info
                assert result["token_data"] == mock_token
                mock_exchange.assert_called_once_with(OAuthProvider.GITHUB, "test_code")
                mock_get_user.assert_called_once_with(OAuthProvider.GITHUB, "test_token")
