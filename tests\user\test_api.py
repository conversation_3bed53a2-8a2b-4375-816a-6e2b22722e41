from fastapi.testclient import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Session

from modules.user.domain.models import UserStatus
from modules.user.infrastructure.orm import UserDB


def should_return_list_of_users_when_valid_request(
    test_client: TestClient, test_user: UserDB
) -> None:
    response = test_client.get("/api/v1/users/")
    assert response.status_code == 200
    users = response.json()
    assert isinstance(users, list)
    assert any(user["username"] == "testuser_api" for user in users)


def should_return_filtered_users_when_status_provided(
    test_client: TestClient, test_user: UserDB
) -> None:
    response = test_client.get("/api/v1/users/?status=active")
    assert response.status_code == 200
    users = response.json()
    assert all(user["status"] == "active" for user in users)


def should_find_users_when_search_query_matches(
    test_client: TestClient, test_user: UserDB
) -> None:
    response = test_client.get("/api/v1/users/search?query=testuser_api")
    assert response.status_code == 200
    users = response.json()
    assert len(users) > 0
    assert users[0]["username"] == "testuser_api"


def should_return_empty_list_when_search_query_does_not_match(
    test_client: TestClient,
) -> None:
    response = test_client.get("/api/v1/users/search?query=non_existing_user")
    assert response.status_code == 200
    users = response.json()
    assert len(users) == 0


def should_update_user_status_when_valid_data(
    test_client: TestClient, test_user: UserDB, db: Session
) -> None:
    update_data = {"status": "inactive"}
    response = test_client.patch(
        f"/api/v1/users/{test_user.id}/status", json=update_data
    )
    assert response.status_code == 200
    assert response.json() is True

    # Verify the update in database - SQLAlchemy Enum field returns enum object
    # Refresh the session to see changes from other transactions
    db.expire_all()
    updated_db_user = db.query(UserDB).filter(UserDB.id == test_user.id).first()
    assert updated_db_user is not None
    assert updated_db_user.status == UserStatus.INACTIVE, (
        f"Expected UserStatus.INACTIVE but got '{updated_db_user.status}'"
    )

    # Verify the update via API - API should return string value
    response = test_client.get(f"/api/v1/users/{test_user.id}")
    assert response.status_code == 200
    user = response.json()
    assert user["status"] == "inactive", (
        f"Expected 'inactive' but got '{user['status']}'"
    )


def should_return_false_when_updating_non_existing_user(
    test_client: TestClient,
) -> None:
    update_data = {"status": "inactive"}
    response = test_client.patch("/api/v1/users/9999/status", json=update_data)
    assert response.status_code == 200
    assert response.json() is False


def should_return_user_when_valid_id_provided(
    test_client: TestClient, test_user: UserDB
) -> None:
    response = test_client.get(f"/api/v1/users/{test_user.id}")
    assert response.status_code == 200
    user = response.json()
    assert user["id"] == test_user.id
    assert user["username"] == test_user.username
    assert user["email"] == test_user.email
    assert user["status"] == "active"


def should_return_404_when_user_not_found(test_client: TestClient) -> None:
    response = test_client.get("/api/v1/users/9999")
    assert response.status_code == 404
    assert response.json()["detail"] == "User with ID 9999 not found"


def should_return_422_when_invalid_status_provided(
    test_client: TestClient, test_user: UserDB
) -> None:
    update_data = {"status": "INVALID_STATUS"}
    response = test_client.patch(
        f"/api/v1/users/{test_user.id}/status", json=update_data
    )
    assert response.status_code == 422
