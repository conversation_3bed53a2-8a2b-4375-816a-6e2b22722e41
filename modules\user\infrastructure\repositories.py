from sqlalchemy import or_
from sqlalchemy.orm import Session

from modules.user.domain.models import User, UserStatus
from modules.user.domain.repositories import UserRepository

from .orm import UserDB

# --- Data Transfer Functions ---


def to_domain(db_user: UserDB) -> User:
    """Converts ORM model to domain model."""
    return User(
        id=int(db_user.id) if db_user.id else 0,
        username=str(db_user.username),
        email=str(db_user.email),
        hashed_password=str(db_user.hashed_password)
        if db_user.hashed_password
        else None,
        status=UserStatus(db_user.status.value)
        if hasattr(db_user.status, "value")
        else UserStatus(db_user.status),
    )


def to_orm(user: User) -> UserDB:
    """Converts domain model to ORM model."""
    return UserDB(
        id=user.id,
        username=user.username,
        email=user.email,
        hashed_password=user.hashed_password,
        status=user.status,
    )


# --- SQLAlchemy Repository Implementation ---


class UserRepositoryImpl(UserRepository):
    def __init__(self, db: Session):
        self.db = db

    async def get_by_id(self, user_id: int) -> User | None:
        db_user = self.db.query(UserDB).filter(UserDB.id == user_id).first()
        return to_domain(db_user) if db_user else None

    async def get_by_username(self, username: str) -> User | None:
        db_user = self.db.query(UserDB).filter(UserDB.username == username).first()
        return to_domain(db_user) if db_user else None

    async def get_by_email(self, email: str) -> User | None:
        db_user = self.db.query(UserDB).filter(UserDB.email == email).first()
        return to_domain(db_user) if db_user else None

    async def save(self, user: User) -> User:
        db_user = self.db.query(UserDB).filter(UserDB.id == user.id).first()
        if db_user:
            # Update existing user
            for key, value in user.dict(exclude={"id"}).items():
                setattr(db_user, key, value)
        else:
            # Create new user
            db_user = to_orm(user)
            self.db.add(db_user)

        self.db.commit()
        self.db.refresh(db_user)
        return to_domain(db_user)

    async def delete(self, user_id: int) -> bool:
        db_user = self.db.query(UserDB).filter(UserDB.id == user_id).first()
        if db_user:
            self.db.delete(db_user)
            self.db.commit()
            return True
        return False

    async def update_status(self, user_id: int, status: UserStatus) -> bool:
        db_user = self.db.query(UserDB).filter(UserDB.id == user_id).first()
        if not db_user:
            return False
        # Assign enum object directly to SQLAlchemy Enum field
        db_user.status = status  # type: ignore[assignment]
        self.db.commit()
        return True

    async def list_users(
        self, skip: int = 0, limit: int = 100, status: UserStatus | None = None
    ) -> list[User]:
        query = self.db.query(UserDB)
        if status:
            query = query.filter(UserDB.status == status)
        users_db = query.offset(skip).limit(limit).all()
        return [to_domain(u) for u in users_db]

    async def search_users(
        self, query: str, skip: int = 0, limit: int = 100
    ) -> list[User]:
        search_pattern = f"%{query}%"
        users_db = (
            self.db.query(UserDB)
            .filter(
                or_(
                    UserDB.username.ilike(search_pattern),
                    UserDB.email.ilike(search_pattern),
                )
            )
            .offset(skip)
            .limit(limit)
            .all()
        )
        return [to_domain(u) for u in users_db]


# --- InMemory Repository Implementation (for testing/development) ---


class InMemoryUserRepository(UserRepository):
    def __init__(self) -> None:
        self._users: dict[int, User] = {}
        self._counter = 1

    async def get_by_id(self, user_id: int) -> User | None:
        return self._users.get(user_id)

    async def get_by_username(self, username: str) -> User | None:
        return next(
            (user for user in self._users.values() if user.username == username), None
        )

    async def get_by_email(self, email: str) -> User | None:
        return next(
            (user for user in self._users.values() if user.email == email), None
        )

    async def save(self, user: User) -> User:
        if user.id == 0 or user.id not in self._users:
            user.id = self._counter
            self._counter += 1
        self._users[user.id] = user
        return user

    async def delete(self, user_id: int) -> bool:
        if user_id in self._users:
            del self._users[user_id]
            return True
        return False

    async def update_status(self, user_id: int, status: UserStatus) -> bool:
        if user_id in self._users:
            user = self._users[user_id]
            user.status = status
            return True
        return False

    async def list_users(
        self, skip: int = 0, limit: int = 100, status: UserStatus | None = None
    ) -> list[User]:
        users = list(self._users.values())
        if status:
            users = [u for u in users if u.status == status]
        return users[skip : skip + limit]

    async def search_users(
        self, query: str, skip: int = 0, limit: int = 100
    ) -> list[User]:
        users = [
            u
            for u in self._users.values()
            if query.lower() in u.username.lower() or query.lower() in u.email.lower()
        ]
        return users[skip : skip + limit]
