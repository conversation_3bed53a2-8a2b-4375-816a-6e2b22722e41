from pydantic import BaseModel, EmailStr, Field

# ====================
# Authentication Schemas
# ====================


class LoginRequest(BaseModel):
    """Schema for user login request."""

    username: str = Field(
        ...,
        description="Username or email address",
        json_schema_extra={"example": "john_doe"}
    )
    password: str = Field(
        ...,
        description="User password",
        json_schema_extra={"example": "SecurePass123!"}
    )


# ====================
# Token Schemas
# ====================


class Token(BaseModel):
    """Schema for JWT access token response."""

    access_token: str = Field(
        ...,
        description="JWT access token for authentication",
        json_schema_extra={"example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."},
    )
    token_type: str = Field(
        ...,
        description="Token type (always 'bearer')",
        json_schema_extra={"example": "bearer"},
    )


# ====================
# OAuth Schemas
# ====================


class OAuthCallbackRequest(BaseModel):
    """Schema for OAuth provider callback data."""

    provider: str = Field(
        ...,
        description="OAuth provider name (e.g., 'google', 'github')",
        json_schema_extra={"example": "google"},
    )
    oauth_id: str = Field(
        ...,
        description="Unique user ID from OAuth provider",
        json_schema_extra={"example": "123456789"},
    )
    email: EmailStr = Field(
        ...,
        description="User email from OAuth provider",
        json_schema_extra={"example": "<EMAIL>"},
    )
    name: str | None = Field(
        None,
        description="Optional user display name from OAuth provider",
        json_schema_extra={"example": "John Doe"},
    )
