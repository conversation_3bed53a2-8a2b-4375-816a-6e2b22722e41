from datetime import datetime

from sqlalchemy import Column, DateTime, Enum, Integer, String, func
from sqlalchemy.sql.schema import Column as ColumnType

from common.db.database import Base
from modules.user.domain.models import UserStatus


class UserDB(Base):
    __tablename__ = "users"

    id: ColumnType[int] = Column(Integer, primary_key=True, index=True)
    username: ColumnType[str] = Column(String, unique=True, nullable=False)
    email: ColumnType[str] = Column(String, unique=True, nullable=False)
    hashed_password: ColumnType[str] = Column(String, nullable=False)
    status: ColumnType[UserStatus] = Column(
        Enum(UserStatus), default=UserStatus.ACTIVE, nullable=False
    )
    created_at: ColumnType[datetime] = Column(DateTime, default=func.now())
    updated_at: ColumnType[datetime] = Column(
        DateTime, default=func.now(), onupdate=func.now()
    )
