from pydantic import BaseModel, ConfigDict, EmailStr, Field

from modules.user.domain.models import UserStatus


class UserBase(BaseModel):
    """Base user schema with common fields."""

    username: str = Field(
        ...,
        min_length=3,
        max_length=50,
        description="Unique username (3-50 characters)",
        json_schema_extra={"example": "john_doe"},
    )
    email: EmailStr = Field(
        ...,
        description="Valid email address",
        json_schema_extra={"example": "<EMAIL>"},
    )


class UserCreate(UserBase):
    """Schema for creating a new user."""

    password: str = Field(
        ...,
        min_length=8,
        description="Strong password (minimum 8 characters)",
        json_schema_extra={"example": "SecurePass123!"},
    )


class UserPublic(UserBase):
    """Schema for public user data (excludes sensitive information)."""

    model_config = ConfigDict(from_attributes=True)

    id: int = Field(..., description="Unique user identifier")
    status: UserStatus = Field(..., description="Current user status")


class UserStatusUpdate(BaseModel):
    """Schema for updating user status."""

    status: UserStatus = Field(..., description="New user status")
