from typing import Dict, Any, Optional
from urllib.parse import urle<PERSON><PERSON>
import httpx
from authlib.integrations.httpx_client import AsyncOAuth2Client
from authlib.oauth2 import OAuth2Error

from modules.auth.domain.models import OAuthProvider


class OAuthClientService:
    """Service for handling OAuth client interactions with third-party providers."""
    
    def __init__(
        self,
        github_client_id: str,
        github_client_secret: str,
        google_client_id: str,
        google_client_secret: str,
        redirect_uri: str,
    ):
        self.github_client_id = github_client_id
        self.github_client_secret = github_client_secret
        self.google_client_id = google_client_id
        self.google_client_secret = google_client_secret
        self.redirect_uri = redirect_uri
        
        # OAuth provider configurations
        self._provider_configs = {
            OAuthProvider.GITHUB: {
                "client_id": self.github_client_id,
                "client_secret": self.github_client_secret,
                "authorize_url": "https://github.com/login/oauth/authorize",
                "token_url": "https://github.com/login/oauth/access_token",
                "user_info_url": "https://api.github.com/user",
                "scope": "user:email",
            },
            OAuthProvider.GOOGLE: {
                "client_id": self.google_client_id,
                "client_secret": self.google_client_secret,
                "authorize_url": "https://accounts.google.com/o/oauth2/v2/auth",
                "token_url": "https://oauth2.googleapis.com/token",
                "user_info_url": "https://www.googleapis.com/oauth2/v2/userinfo",
                "scope": "openid email profile",
            },
        }

    def generate_authorization_url(self, provider: OAuthProvider, state: Optional[str] = None) -> str:
        """Generate OAuth authorization URL for the specified provider."""
        if provider not in self._provider_configs:
            raise ValueError(f"Unsupported OAuth provider: {provider}")
        
        config = self._provider_configs[provider]
        
        params = {
            "client_id": config["client_id"],
            "redirect_uri": self.redirect_uri,
            "scope": config["scope"],
            "response_type": "code",
        }
        
        if state:
            params["state"] = state
            
        return f"{config['authorize_url']}?{urlencode(params)}"

    async def exchange_code_for_token(self, provider: OAuthProvider, code: str) -> Dict[str, Any]:
        """Exchange authorization code for access token."""
        if provider not in self._provider_configs:
            raise ValueError(f"Unsupported OAuth provider: {provider}")

        config = self._provider_configs[provider]

        oauth_client = AsyncOAuth2Client(
            client_id=config["client_id"],
            client_secret=config["client_secret"],
        )

        try:
            token = await oauth_client.fetch_token(
                config["token_url"],
                authorization_response=f"{self.redirect_uri}?code={code}",
            )
            return token
        except OAuth2Error as e:
            raise ValueError(f"Failed to exchange code for token: {str(e)}") from e

    async def get_user_info(self, provider: OAuthProvider, access_token: str) -> Dict[str, Any]:
        """Fetch user information from OAuth provider using access token."""
        if provider not in self._provider_configs:
            raise ValueError(f"Unsupported OAuth provider: {provider}")
        
        config = self._provider_configs[provider]
        
        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {access_token}"}
            
            try:
                response = await client.get(config["user_info_url"], headers=headers)
                response.raise_for_status()
                user_data = response.json()
                
                # Normalize user data across providers
                return self._normalize_user_data(provider, user_data)
            except httpx.HTTPError as e:
                raise ValueError(f"Failed to fetch user info: {str(e)}") from e

    def _normalize_user_data(self, provider: OAuthProvider, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Normalize user data from different OAuth providers to a common format."""
        if provider == OAuthProvider.GITHUB:
            return {
                "oauth_id": str(raw_data["id"]),
                "email": raw_data.get("email"),
                "name": raw_data.get("name"),
                "username": raw_data.get("login"),
                "avatar_url": raw_data.get("avatar_url"),
            }
        elif provider == OAuthProvider.GOOGLE:
            return {
                "oauth_id": raw_data["id"],
                "email": raw_data.get("email"),
                "name": raw_data.get("name"),
                "username": raw_data.get("email", "").split("@")[0] if raw_data.get("email") else None,
                "avatar_url": raw_data.get("picture"),
            }
        else:
            raise ValueError(f"Unsupported provider for data normalization: {provider}")

    async def handle_oauth_flow(self, provider: OAuthProvider, code: str) -> Dict[str, Any]:
        """Complete OAuth flow: exchange code for token and fetch user info."""
        # Exchange code for access token
        token_data = await self.exchange_code_for_token(provider, code)
        access_token = token_data["access_token"]
        
        # Fetch user information
        user_info = await self.get_user_info(provider, access_token)
        
        return {
            "user_info": user_info,
            "token_data": token_data,
        }
