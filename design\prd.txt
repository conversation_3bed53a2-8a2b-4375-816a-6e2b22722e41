构建 MCP 服务器市场功能报告1. 执行摘要本报告旨在为开发一个 Model Context Protocol (MCP) 服务器市场平台提供详细的功能需求说明书、领域驱动设计（DDD）模型以及敏捷用户故事。该平台的目标是促进 MCP 服务器的发现、评估和集成，并借鉴现有平台（如 Glama.ai）的能力，同时加以增强。分析发现，MCP 生态系统是一个复杂的互联网络，需要强大的搜索、过滤、详细的服务器信息展示以及社区贡献功能。核心的 MCP 服务器 实体具有丰富的属性，这使得采用成熟的领域驱动设计方法变得至关重要。基于这些发现，建议采用分阶段的敏捷开发方法，利用详细的用户故事指导 AI Agent 进行代码生成。初期应重点关注核心服务器列表和发现功能，随后再逐步引入服务器提交和社区互动功能。2. Model Context Protocol (MCP) 生态系统介绍2.1. MCP 的目的与核心概念Model Context Protocol (MCP) 是一种开放协议，旨在标准化应用程序如何向大型语言模型 (LLM) 提供上下文信息 1。它类似于 AI 应用程序的 USB-C 端口，提供了一种标准化的方法来连接 AI 模型与各种数据源和工具。MCP 的核心目的是促进在 LLM 之上构建 AI 代理和复杂工作流。它解决了 LLM 频繁需要与数据和工具集成的问题，通过提供：不断增长的预构建集成列表，LLM 可以直接插入；在不同 LLM 提供商和供应商之间切换的灵活性；以及在现有基础设施内保护数据的最佳实践 1。这种协议的出现，反映了 LLM 越来越需要超越其静态训练数据，与实时、动态的信息和外部系统进行交互。MCP 旨在解决 LLM 的“上下文问题”，使其能够访问和利用外部数据与工具来执行更复杂的任务。这种标准化促进了一个生态系统的形成，其中各种应用程序和数据源可以轻松地“插入”并增强 LLM 的能力，从而加速了复杂 AI 代理和工作流的开发。它还有助于减少 LLM 供应商的锁定效应，并促进互操作性。2.2. 关键组件与通用架构MCP 的核心在于其客户端-服务器架构，允许一个宿主应用程序连接到多个服务器 1。该架构的关键组件包括：MCP 宿主 (MCP Hosts)：这些是需要通过 MCP 访问数据的程序或应用程序，例如 Claude Desktop、集成开发环境 (IDE) 或其他 AI 工具 1。MCP 客户端 (MCP Clients)：这些是协议客户端，与 MCP 服务器保持一对一连接 1。MCP 服务器 (MCP Servers)：这些是轻量级程序，每个程序都旨在通过标准化的 Model Context Protocol 暴露特定功能 1。MCP 服务器提供的功能主要分为三类：资源（客户端可以读取的文件式数据，如 API 响应或文件内容）、工具（经用户批准后可由 LLM 调用的函数）和提示（旨在帮助用户完成特定任务的预编写模板） 2。例如，一个天气服务器可以暴露 get-alerts（获取美国某个州的天气警报）和 get-forecast（获取特定经纬度的天气预报）这两个工具 2。在开发层面，MCP 服务器支持自动工具定义。使用 Python 的 FastMCP 等 SDK，工具定义可以从 Python 类型提示和文档字符串中自动生成，从而简化了 MCP 工具的创建和维护。同样，Java 的 Spring AI 中的 @Tool 注解可以自动将服务注册为工具 2。MCP 服务器与客户端之间的通信传输主要使用 stdio（标准输入/输出）2。集成 MCP 服务器通常通过将服务器的命令和参数添加到客户端的配置文件（例如 claude_desktop_config.json）中来完成 2。调试对于 MCP 服务器的连接和故障排除至关重要，Claude Desktop 会将 MCP 相关的日志写入 mcp.log 和 mcp-server-SERVERNAME.log 文件 2。本地数据源 (Local Data Sources)：指用户计算机上存在的数据，包括文件、数据库和服务，MCP 服务器可以安全地访问这些数据 1。远程服务 (Remote Services)：指通过互联网可访问的外部系统，通常通过 API，MCP 服务器可以连接到这些系统 1。这种 MCP 生态系统本质上是分布式的，每个服务器都提供特定的功能。为了使这个生态系统蓬勃发展，用户（包括开发者、AI 代理和最终用户）必须有一种高效的方式来发现可用的服务器，了解它们的功能，并学习如何集成它们。MCP 服务器市场网站正是扮演了这一角色，它充当了分散式 MCP 服务器网络的中央注册和发现门户。如果没有这样一个平台，服务器的发现能力将受到严重限制，从而阻碍 MCP 的采用和实用性。这表明，该市场不仅仅是一个静态列表，它是一个连接到不断发展和演变的协议的动态接口。2.3. MCP 服务器市场概念概述MCP 服务器市场是一个集中式目录，用于展示“开源 MCP 服务器”，这些服务器通过文件访问、数据库连接、API 集成以及其他上下文服务来扩展 AI 能力 3。它为用户提供了一个平台，以查找、评估并可能安装或集成这些服务器。鉴于 MCP 服务器可以访问本地数据和远程服务，并且旨在集成到关键的 AI 工作流中，信任和质量变得至关重要。市场平台通过提供“质量/安全/许可证指标”（例如“Asecurity”、“Alicense”、“Aquality”）4 和反馈机制（例如“这有用吗？是/否”按钮）3，隐含地承担了对这些服务器进行策展和验证的角色。这表明该平台不仅仅是一个列出服务，更是一个把关者和信任建立者，这对于开放协议的广泛采用至关重要，因为在开放协议中任何人都可以贡献。此功能对于降低将第三方代码集成到敏感 AI 应用程序中的风险至关重要。3. 现有 MCP 服务器市场 (Glama.ai) 功能分析3.1. 首页功能与用户体验尽管提供的资料没有直接详细说明 Glama.ai 首页的具体功能或显示方式，但其服务器列表页面的存在 3 表明，首页很可能作为核心功能的明确入口点。首页可能主要用于引导用户进入服务器列表，并可能突出显示特色服务器、最新更新或关键类别。3.2. MCP 服务器列表与发现3.2.1. 显示元素与信息服务器列表页面显示了“开源 MCP 服务器”的总数（例如，“6,013 台服务器”）以及“上次更新”的时间戳 3。每个服务器条目都包含丰富的详细信息，以帮助用户快速了解其特性：服务器名称：通常指示其主要功能，并作为可点击的链接 5。官方标签：许多条目标有“官方”标签，表明它们由相关组织正式支持或开发 5。开发者/组织：开发或维护服务器的实体名称，也是可点击的链接 5。质量/安全/许可证指标：每个服务器都有“Asecurity”、“Alicense”和“Aquality”的指标，提供有关服务器安全性、许可和整体质量的信息 5。描述：简要说明 MCP 服务器的功能以及它提供的数据或功能类型 5。上次更新：指示服务器上次更新的时间戳 5。编程语言：指定服务器所使用的编程语言，例如“TypeScript”、“Python”5。许可证类型：服务器发布的软件许可证类型，例如“MIT 许可证”、“Apache 2.0”5。操作系统兼容性：显示与不同操作系统兼容的图标或文本，例如“Apple”和“Linux”5。每个服务器条目中包含的丰富元数据（如语言、许可证、操作系统兼容性、质量指标）远不止简单的命名。这些广泛的元数据对于用户快速评估服务器的相关性、可信度和技术适用性至关重要，而无需点击进入每个详细页面。对于生成代码的 AI Agent 而言，这种结构化的元数据在根据预定义条件（例如，“查找具有 MIT 许可证且支持 Linux 并具有 Asecurity 评级的 Python 服务器”）过滤和选择适当服务器时具有不可估量的价值。这表明，市场的价值与其服务器元数据的完整性和准确性密切相关。3.2.2. 筛选、排序与分类机制服务器列表页面提供了多种排序选项，包括：搜索相关性（默认排序选项）、添加日期、更新日期、每周下载量、GitHub 星数以及最近的 GitHub 星数 3。此外，页面还列出了众多类别，每个类别旁边都显示有计数，这些类别可作为过滤选项。例如，包括 Python (2,436)、远程 (2,425)、TypeScript (1,988)、开发工具 (1,703)、数据库 (715)、RAG 系统 (637)、代码执行 (577)、代理编排 (549)、自治代理 (546)、官方 (454)、网页抓取 (304)、云平台 (298)、通信 (250)、浏览器自动化 (241)、内容管理系统 (230)、项目管理 (216)、知识与记忆 (213)、金融 (208)、文档访问 (207)、文件系统 (206)、区块链 (200)、图像与视频处理 (167)、API 测试 (163)、代码分析 (158)、安全 (155) 等 3。这些类别被组织成导航菜单，并有特定的类别页面（例如 /categories/search、/categories/databases）显示与该类别相关的服务器 6。在特定类别内，服务器以连续可滚动的列表形式呈现，没有进一步的子分类或过滤选项 6。这种广泛的类别列表（超过 30 个）表明存在一个高度细粒度的分类系统。虽然这允许进行精确过滤，但它也给用户导航和发现带来了挑战。平台必须在为特定需求（例如“区块链”、“RAG 系统”）提供足够详细信息与确保用户不会感到信息过载之间取得平衡。这可能需要分层分类或强大的标签系统，以允许进行广泛和狭窄的搜索。类别中较高的计数也意味着服务器数量庞大，这进一步强调了高效过滤的必要性。3.2.3. 搜索功能与结果呈现页面上提供了“深度搜索”和“新搜索”按钮，这表明除了简单的关键词匹配之外，还存在高级搜索功能 3。搜索结果页面（例如 /categories/search）显示了“用于搜索的 MCP 服务器”列表，每个条目都包含标准的服务器详细信息 5。该页面隐含的搜索功能是“使用自然语言访问和查询各种来源的数据”的能力 5。搜索类别中服务器的描述突出了其多样化的功能，例如：按维度过滤和检索指标（Clarity Data Export MCP Server）、实时公共网络数据访问（Bright Data MCP）、特定领域搜索（Tavily MCP Server）、SEO 数据访问（DataForSEO MCP Server）、区块链数据查询（Satstream MCP Server）、经济数据访问（MCP-FREDAPI）等 5。该平台提供了以人为中心的搜索和过滤功能（关键词、类别、排序），但同时也列出了其主要目的在于使 AI Agent 执行自然语言搜索的服务器。这形成了一种有趣的二元性：市场平台本身可供人类搜索，而市场上的产品（MCP 服务器）则使 AI 能够搜索其他数据。这意味着平台自身的搜索功能必须足够强大，以允许用户找到“正确的 AI 搜索工具”，从而使元搜索功能成为一项关键特性。这也表明，该平台最终可能会为其自身的服务器目录集成 AI 驱动的搜索功能。3.3. 单个 MCP 服务器详情视图单个服务器的详细页面（例如 ChainGPT MCP）提供了服务器的全面概述，包括其目的、功能、设置说明、安装方法、故障排除提示、可用工具和相关资源 4。页面上突出显示的信息包括：服务器标识：服务器标题（“ChainGPT MCP”）和作者（“by kohasummons”）4。元数据：与服务器相关的标签/关键词（GitHub、Blockchain、Search、Cryptocurrency、TypeScript）、许可证（MIT 许可证）以及兼容性/支持平台（Apple）4。导航链接：提供了一组内部导航链接，可快速访问页面的不同部分：概述、InspectNew、Schema、相关服务器、评论、评分和需要帮助？4。开发链接：提供“查看源代码”（GitHub）和“报告问题”的链接 4。服务器功能（“您可以使用此服务器做什么？”）：详细说明了 ChainGPT MCP 服务器的核心功能，例如与 ChainGPT AI 交互、访问加密新闻、跟踪加密市场、分析区块链数据以及检索聊天历史记录 4。可用集成（“此服务器有哪些集成可用？”）：概述了技术要求和集成点，例如支持 macOS 系统上的配置以与 Claude Desktop 应用程序集成，需要 Node.js 环境 (v18 或更高版本)，并支持使用 npm/npx 命令进行安装和执行 4。设置：设置服务器的先决条件，例如获取 ChainGPT 密钥、Node.js 环境和兼容的 MCP 客户端 4。安装：详细的安装说明，包括通过 Smithery 或手动安装的命令行指令，以及配置 Claude Desktop 以识别 ChainGPT MCP 服务器的步骤和 JSON 配置片段 4。故障排除：专门用于常见问题及其解决方案的部分，例如服务器未找到、API 密钥问题、连接问题、Node.js 版本要求和工具调用超时 4。工具：一个表格列出了可用的工具，包括它们的名称、描述和示例提示，例如 chaingpt_invoke_chat 和 chaingpt_get_news 4。贡献：一个鼓励拉取请求和问题讨论的标准部分 4。质量指标：显示“Asecurity – 无已知漏洞”、“Alicense – 宽松许可证”和“Aquality – 确认可用”，并提供指向其计算方法的链接 4。技术细节：指示服务器是否“远程可用”，并提供 HTTP 连接 URL 4。相关资源：指向外部讨论的链接，例如“关于此服务器的 Reddit 讨论”4。相关 MCP 服务器：列出其他相关的 MCP 服务器，包括其作者、安全、许可证、质量、描述、上次更新日期、技术和兼容平台 4。新 MCP 服务器：列出最近添加的 MCP 服务器 4。MCP 目录 API：提供通过 MCP API 访问服务器信息的信息，包括 curl 命令示例和 Discord 服务器链接 4。用户反馈：“这有用吗？是/否”按钮 4。行动呼吁：“安装服务器”按钮 4。需要注意的是，inspect、schema 和 related-servers 子页面无法访问 7。然而，与模式（通过工具）和相关服务器相关的信息已在主详细页面上呈现。服务器详细页面（4）上信息的数量和特异性表明，它不仅是一个产品页面，而且是每个服务器的迷你文档门户。这对于需要精确集成、设置和故障排除说明的开发人员和 AI Agent 至关重要。包含工具的示例提示对于 LLM 和 AI Agent 尤其有价值。这意味着平台必须支持富文本编辑、代码片段和结构化数据输入，以供服务器提交，从而本质上成为一个开发人员文档的内容管理系统。3.4. 用户交互与贡献功能服务器列表页面上存在“添加服务器”按钮 3，以及服务器详细页面上的“报告问题”和“贡献”部分 4，都表明平台鼓励用户参与。此外，在列表和详细页面上都提供了“这有用吗？是/否”的反馈机制 3。为了支持社区，平台还提供了指向 Reddit 和 Discord 社区的社交媒体链接，并支持新闻通讯注册 3。“添加服务器”、“报告问题”、“贡献”和反馈机制的存在，表明平台旨在构建一个社区驱动的生态系统。对于像 MCP 这样的开放协议，社区贡献对于其增长和多样化至关重要。市场不仅需要促进发现，还需要积极鼓励和简化通过用户参与进行服务器提交、维护和改进的过程。这意味着需要强大的用户身份验证、内容审核，以及可能超越简单有用性反馈的评论/评分系统。3.5. MCP 服务器关键属性与能力总结下表总结了从现有平台分析中识别出的 MCP 服务器的关键属性和能力。这张表格为开发人员和 AI Agent 提供了一个简洁的参考，突出了定义 MCP 服务器在市场上的关键元数据点。这种结构化的视图对于人类理解和机器处理（例如，AI Agent 需要理解服务器属性以生成代码）都至关重要。表：MCP 服务器关键属性与能力属性/能力描述来源片段重要性/目的服务器名称服务器的唯一标识符，通常指示其功能。3发现、识别作者/组织服务器的创建者或维护者。4归属、可信度简要描述服务器功能的简洁概述。5快速理解详细描述服务器目的和功能的全面说明。4深入理解标签/关键词与服务器相关的分类词，用于过滤和搜索。4发现、分类许可证类型服务器软件的许可协议（例如 MIT 许可证）。4法律合规性、使用限制编程语言服务器的实现语言（例如 Python, TypeScript）。2技术栈兼容性、开发人员偏好操作系统兼容性服务器支持的操作系统（例如 Apple, Linux）。4部署环境兼容性质量评分 (Asecurity, Alicense, Aquality)指示服务器的安全性、许可证合规性和整体质量。4信任、可靠性评估远程能力指示服务器是否可以远程托管和运行。4部署灵活性HTTP 连接 URL用于直接连接服务器的 URL。4直接集成提供的能力 (资源, 工具, 提示)服务器暴露给客户端的核心功能类型。2功能分类、LLM 交互类型工具定义 (名称, 描述, 示例提示, 模式)服务器提供的具体函数，包括其输入/输出结构。2LLM 调用、功能理解、代码生成设置说明运行服务器所需的先决条件（例如 API 密钥）。4部署准备安装方法详细的安装步骤和命令（例如 npm/npx, pnpm）。4部署指南故障排除提示常见问题及其解决方案。4用户支持、问题解决源代码 URL指向服务器代码仓库的链接。4透明度、贡献、审查问题跟踪器 URL用于报告和跟踪服务器问题的链接。4错误报告、维护上次更新日期服务器信息或代码上次更新的时间。3活跃度、新近度每周下载量/GitHub 星数服务器的受欢迎程度指标。3流行度、社区认可相关服务器与当前服务器功能相似的其他服务器列表。4发现替代方案、扩展探索类别服务器所属的功能类别（例如 数据库、搜索）。3分类浏览、过滤4. 新平台功能需求为了构建一个功能丰富且用户友好的 MCP 服务器市场，以下是根据现有平台分析和未来发展需求提出的功能需求。4.1. 用户管理与认证用户必须能够注册、登录和管理他们的个人资料。虽然现有资料没有明确详细说明用户账户，但“添加服务器”按钮 3 和服务器作者的显示（例如“by kohasummons”）4 表明，用户账户对于归属和贡献是必需的。这是任何允许用户生成内容的平台的基础要求。需求：用户必须能够注册、登录和管理他们的个人资料。详细信息：提供安全的认证机制（例如，OAuth、电子邮件/密码）。允许用户编辑个人资料（显示名称、联系信息）。支持密码管理功能。4.2. MCP 服务器提交与生命周期管理开发者必须能够向市场提交新的 MCP 服务器并管理他们现有的列表。需求：开发者必须能够向市场提交新的 MCP 服务器并管理他们现有的列表。详细信息：服务器提交表单：捕获所有在 4 和 5 中识别的属性（服务器标题、作者、描述、标签、许可证、兼容性、功能概述、详细功能、集成、设置说明、安装方法、故障排除、工具（包括名称、描述、示例提示）、源代码 URL、问题跟踪器 URL、HTTP 连接 URL）。工具定义输入：为单个工具提供结构化输入，包括其 OpenAPI/JSON Schema 定义（4 中的“Schema”导航链接和 2 中的“Tools”表格暗示）。服务器提交过程不仅仅是简单的文本输入。它是一个复杂的、结构化的数据摄取流程，需要以对开发者友好且对 AI Agent 机器可读的方式捕获结构化信息（例如，工具模式、安装步骤、故障排除点）。这需要支持富文本编辑器、代码块，并可能在提交过程中进行模式验证，以确保数据质量。质量指标输入/生成：提供提交或生成 Asecurity、Alicense、Aquality 评分的机制。这可能涉及自动化检查或人工审核 4。草稿/发布工作流：支持保存草稿、预览以及发布/取消发布服务器的功能。编辑/更新：允许作者更新服务器信息。版本控制：支持服务器的不同版本（3 和 5 中的“上次更新”暗示）。审核：对新提交和更新进行管理审核流程。4.3. 高级服务器发现与探索用户必须能够通过全面的搜索、过滤和分类有效地找到相关的 MCP 服务器。需求：用户必须能够通过全面的搜索、过滤和分类有效地找到相关的 MCP 服务器。详细信息：关键词搜索：支持对服务器名称、描述、功能和标签进行全文搜索。支持“深度搜索”和“新搜索”功能 3。分类：显示并按预定义类别（例如 Python、远程、数据库、RAG 系统、区块链等）进行过滤，并显示每个类别的服务器计数 3。高级过滤：按编程语言、许可证类型、操作系统兼容性、“官方”状态、质量评分（Asecurity、Alicense、Aquality）进行过滤 5。排序：按搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数、最近 GitHub 星数进行排序 3。分页/无限滚动：高效加载大量服务器列表，支持“加载更多”按钮 3。详细的过滤和排序选项不仅适用于人类用户，对于 AI Agent 也至关重要。一个 AI Agent 可以通过编程方式查询市场 API（4 中“MCP 目录 API”的提示）来查找符合特定条件的服务器（例如，“查找所有具有 Asecurity 评级的基于 Python 的 RAG 系统服务器”）。这意味着底层的搜索和过滤逻辑必须健壮、高性能，并且最好通过 API 暴露以供编程访问，从而使该平台成为自动化 AI 开发工作流的宝贵资源。4.4. 详细服务器信息显示与交互为每个 MCP 服务器提供一个全面且结构良好的详细页面。需求：为每个 MCP 服务器提供一个全面且结构良好的详细页面。详细信息：显示 4 中指定的所有属性和功能（服务器标题、作者、标签、许可证、兼容性、功能、集成、设置、安装、故障排除、工具、质量指标等）。提供交互式“安装服务器”按钮（可能根据客户端动态显示说明）4。提供“查看源代码”和“报告问题”的可点击链接 4。清晰呈现“工具”部分，包括描述和示例提示 4。显示“相关 MCP 服务器”和“新 MCP 服务器”以增强发现能力 4。与外部资源（例如 Reddit 讨论）集成 4。提供 HTTP 连接 URL 以便直接集成 4。“安装服务器”按钮 4 是一个关键的行动呼吁。它的有效性取决于提供针对各种 MCP 客户端（例如 Claude Desktop、IDE）量身定制的清晰、可操作的说明。这表明需要根据检测到的客户端或用户偏好进行动态内容生成或条件显示安装步骤。此功能直接将市场的发现功能与服务器在 AI 应用程序中的实际集成连接起来。4.5. 社区功能使用户能够提供反馈并与平台和服务器开发者互动。需求：使用户能够提供反馈并与平台和服务器开发者互动。详细信息：提供页面“这有用吗？是/否”的反馈机制 3。支持对单个服务器进行评论和评分（4 中“评论”和“评分”导航链接暗示）。提供讨论论坛或与现有社区平台（例如 3 和 4 中提到的 Discord、Reddit 链接）集成。支持新闻通讯注册以获取更新 3。反馈机制（有用性、评论、问题报告）对于 MCP 生态系统的健康和发展至关重要。它们为服务器开发者提供改进的宝贵信号，并为其他用户评估服务器质量和可靠性提供依据。这形成了一个良性循环：良好的反馈促成更好的服务器，从而带来更多的采用，进一步丰富了市场。平台必须有效地捕获、聚合和显示这些反馈。4.6. 平台管理与监控提供工具供管理员管理内容、用户并监控平台健康状况。需求：提供工具供管理员管理内容、用户并监控平台健康状况。详细信息：用户账户管理（暂停、删除）。服务器提交和评论的内容审核。类别管理（添加、编辑、删除类别）。分析仪表板（服务器下载量、视图、搜索查询）。错误日志和监控（例如，用于处理服务器 API 访问问题）。这些功能对于维护平台完整性、质量和可扩展性至关重要，尤其是在处理用户生成内容时。5. 领域驱动设计 (DDD) 核心实体模型为了有效构建 MCP 服务器市场，采用领域驱动设计（DDD）方法至关重要，它有助于将复杂的业务逻辑分解为可管理的部分。5.1. 边界上下文识别系统被划分为以下边界上下文，每个上下文都围绕特定的领域功能和一致性边界：服务器目录上下文 (Server Catalog Context)：负责管理 MCP 服务器列表、发现、搜索和详细信息。这是市场的核心公共面向部分。服务器提交与管理上下文 (Server Submission & Management Context)：处理服务器提交、更新和开发者特定交互的生命周期。用户与身份上下文 (User & Identity Context)：管理用户注册、身份验证、个人资料和角色。社区与反馈上下文 (Community & Feedback Context)：管理评论、评分和通用平台反馈。管理上下文 (Administration Context)：处理平台级别的配置、审核和分析。将系统分解为不同的边界上下文是一项战略性的 DDD 决策。它避免了单体设计，降低了开发团队的认知负担，并允许系统不同部分的独立演进和扩展。例如，服务器目录 可以针对读取性能进行高度优化，而 服务器提交 则专注于数据完整性和工作流。这种分离对于需要满足不同用户角色和功能的复杂系统至关重要。5.2. 聚合根、实体与值对象详细建模以下是核心边界上下文中的聚合根、实体和值对象的详细模型：5.2.1. 服务器目录上下文聚合根: MCP_Server标识: serverId (UUID)属性 (值对象/基本类型):ServerIdentity: name (字符串), author (字符串), slug (字符串, 唯一的 URL 标识符)。Description: shortDescription (字符串), longDescription (富文本)。Metadata: licenseType (枚举/字符串, 例如 "MIT License"), programmingLanguage (枚举/字符串, 例如 "Python"), supportedOperatingSystems (枚举集合, 例如 "Apple", "Linux")。QualityMetrics: securityScore (枚举/字符串, 例如 "Asecurity"), licenseScore (枚举/字符串, 例如 "Alicense"), qualityScore (枚举/字符串, 例如 "Aquality")。IntegrationDetails: httpConnectionUrl (URL), remoteCapable (布尔值)。Timestamps: dateAdded (DateTime), lastUpdated (DateTime)。PopularityMetrics: weeklyDownloads (整数), gitHubStars (整数), recentGitHubStars (整数)。聚合内的实体:ServerCapability: 代表服务器可以做什么。type (枚举: RESOURCE, TOOL, PROMPT)description (字符串)details (富文本, 例如 "与 ChainGPT AI 交互")ToolDefinition: 代表服务器暴露的特定函数。toolName (字符串, 例如 chaingpt_invoke_chat)toolDescription (字符串)examplePrompts (字符串列表)schema (JSON/OpenAPI 模式, 代表输入/输出结构)IntegrationStep: 集成服务器的详细说明。platform (字符串, 例如 "Claude Desktop", "Smithery")method (字符串, 例如 "手动安装", "通过 Smithery")instructions (包含代码片段的富文本)TroubleshootingTip: 常见问题和解决方案。issue (字符串)solution (富文本)值对象:Category: name (字符串, 例如 "数据库", "搜索"), count (整数)。Tag: name (字符串, 例如 "区块链", "TypeScript")。ExternalLink: url (URL), type (枚举: GITHUB_SOURCE, ISSUE_TRACKER, REDDIT_DISCUSSION)。聚合根: Category标识: categoryId (UUID)属性: name (字符串, 唯一), slug (字符串), description (字符串), serverCount (整数)。关系: MCP_Server 与 Category 之间存在多对多关系（通过 Tag 或直接关联）。5.2.2. 用户与身份上下文聚合根: User标识: userId (UUID)属性: username (字符串), email (字符串), passwordHash (字符串), profileName (字符串), roles (枚举集合: DEVELOPER, USER, ADMIN)。聚合内的实体: UserProfile (例如, 个人简介, 头像)。5.2.3. 社区与反馈上下文聚合根: Review标识: reviewId (UUID)属性: rating (整数), comment (字符串), timestamp (DateTime)。关系: Review 属于 User (评论者) 和 MCP_Server (被评论的服务器)。实体: Feedback标识: feedbackId (UUID)属性: pageUrl (URL), helpful (布尔值), comment (可选字符串), timestamp (DateTime)。关系: Feedback 可以与 User 关联。5.3. 关键关系与领域不变性一个 MCP_Server 必须有一个唯一的 slug。MCP_Server 内的 ToolDefinition 必须有唯一的 toolName。QualityMetrics 应该一致地应用，并可能基于外部数据（例如，GitHub 仓库分析）进行计算。一个 Review 必须链接到一个现有的 User 和 MCP_Server。Category 必须有唯一的名称。表：DDD 模型 - 核心实体、属性与关系下表提供了所识别领域模型的清晰、结构化概述，可作为数据库模式设计和后端服务开发的蓝图。它明确定义了聚合、实体和值对象，以及它们的关键属性和关系，这对于 AI Agent 生成数据模型和 ORM 代码至关重要。边界上下文领域对象类型名称关键属性关系关键不变性/规则服务器目录聚合根MCP_ServerserverId, ServerIdentity, Description, Metadata, QualityMetrics, IntegrationDetails, Timestamps, PopularityMetrics包含 ServerCapability, ToolDefinition, IntegrationStep, TroubleshootingTip 实体；与 Category 多对多slug 唯一；ToolDefinition.toolName 在 MCP_Server 内唯一实体ServerCapabilitytype, description, details属于 MCP_Server-实体ToolDefinitiontoolName, toolDescription, examplePrompts, schema属于 MCP_Server-实体IntegrationStepplatform, method, instructions属于 MCP_Server-实体TroubleshootingTipissue, solution属于 MCP_Server-值对象Categoryname, count--值对象Tagname--值对象ExternalLinkurl, type--服务器目录聚合根CategorycategoryId, name, slug, description, serverCount与 MCP_Server 多对多name 唯一用户与身份聚合根UseruserId, username, email, passwordHash, profileName, roles包含 UserProfile 实体username, email 唯一实体UserProfilebio, avatar属于 User-社区与反馈聚合根ReviewreviewId, rating, comment, timestamp属于 User，关联 MCP_Server必须链接到现有 User 和 MCP_Server实体FeedbackfeedbackId, pageUrl, helpful, comment, timestamp可关联 User-6. 敏捷用户故事以下用户故事遵循“作为 [用户角色]，我想要 [行动]，以便 [益处]”的格式，并确保符合 INVEST 原则（独立、可协商、有价值、可估算、小、可测试）。每个故事都包含详细的验收标准，为 AI Agent 进行相关的代码生成提供清晰、明确的步骤。6.1. 用户故事通用原则遵循“作为 [用户角色]，我想要 [行动]，以便 [益处]”的格式。确保故事符合 INVEST 原则（独立、可协商、有价值、可估算、小、可测试）。每个故事都有详细的验收标准，提供清晰、明确的测试步骤和 AI Agent 代码生成指导。重点关注具体的数据点和交互，以提高 AI Agent 的清晰度。6.2. 服务器发现与列表用户故事US-001: 查看所有 MCP 服务器描述：作为一名用户，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。验收标准：当我导航到服务器列表页面 (/mcp/servers) 时。我看到“开源 MCP 服务器”的总数（例如，“6,013 台服务器”）3。我看到整个列表的“上次更新”时间戳 3。我看到一个分页/可滚动的 MCP 服务器条目列表。每个条目显示：服务器名称、官方标签（如果适用）、开发者/组织、Asecurity、Alicense、Aquality 指标、简要描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标（Apple、Linux）5。当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目 3。US-002: 按类别筛选服务器描述：作为一名用户，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域（例如，“数据库”、“Python”）相关的服务器。验收标准：当我位于服务器列表页面 (/mcp/servers) 时。当我从侧边栏/筛选选项中选择一个类别（例如，“Python”）时。URL 更新以反映该类别（例如，/mcp/servers/categories/python）6。显示的列表仅显示标记为“Python”的服务器 3。“Python”的类别计数准确（例如，“2,436”）3。每个显示的服务器条目都符合 US-001 的标准。US-003: 按受欢迎程度/新近度排序服务器描述：作为一名用户，我想要按各种标准（如下载量或最近更新）对服务器列表进行排序，以便我能优先查看热门或最新活跃的服务器。验收标准：当我位于服务器列表页面时。当我从排序下拉菜单中选择“每周下载量”时。服务器列表重新排序，以最高每周下载量的服务器优先显示 3。当我从排序下拉菜单中选择“更新日期”时。服务器列表重新排序，以最近更新的服务器优先显示 3。排序选项包括：搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数、最近 GitHub 星数 3。US-004: 按关键词搜索服务器描述：作为一名用户，我想要使用关键词搜索服务器，以便我能快速找到特定的服务器或功能。验收标准：当我位于服务器列表页面时。当我在搜索栏中输入“区块链”并按 Enter 键时。页面显示名称、描述或标签中包含“区块链”的服务器列表 5。“深度搜索”和“新搜索”按钮可用 3。每个显示的服务器条目都符合 US-001 的标准。US-005: 查看单个服务器详情描述：作为一名用户，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。验收标准：当我从服务器列表中点击一个服务器条目（例如，“ChainGPT MCP”）时。我被导航到服务器的详情页面（例如，/mcp/servers/@kohasummons/chaingpt-mcp）4。页面突出显示：服务器标题、作者、标签、许可证、兼容性 4。页面包含导航链接：概述、Schema、相关服务器、评论、评分 4。页面提供“查看源代码”和“报告问题”链接 4。“您可以使用此服务器做什么？”部分详细说明了核心功能 4。“此服务器有哪些集成可用？”部分概述了技术要求和集成点 4。“设置”部分列出了先决条件（例如 API 密钥、Node.js 版本）4。“安装”部分提供了详细说明（例如 npx 命令、pnpm 命令、Claude Desktop 的 JSON 配置）4。“故障排除”部分列出了常见问题和解决方案 4。“工具”表格列出了每个工具的名称、描述和示例提示 4。质量指标（Asecurity、Alicense、Aquality）显示并附有其计算方法的链接 4。“安装服务器”按钮和 HTTP 连接 URL 存在 4。“相关 MCP 服务器”部分显示了类似服务器的列表 4。“新 MCP 服务器”部分显示了最近添加的服务器 4。“这有用吗？是/否”反馈按钮可用 4。6.3. 服务器提交与管理用户故事US-006: 提交新的 MCP 服务器描述：作为一名开发者，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。验收标准：当我以开发者身份登录时。当我点击“添加服务器”按钮时 3。我看到一个多步骤表单，用于输入服务器详细信息。表单包含以下字段：服务器名称、作者（从个人资料自动填充，可编辑）、简要描述、详细描述（富文本编辑器）、许可证类型、编程语言、支持的操作系统、源代码 URL、问题跟踪器 URL、HTTP 连接 URL、远程可用复选框 2。我可以添加多个“功能”，包括类型（资源、工具、提示）和描述 2。我可以添加多个“工具”，包括名称、描述、示例提示，以及 JSON Schema 定义字段 2。我可以添加多个“集成步骤”，包括平台、方法和详细说明（带代码块的富文本）4。我可以添加多个“故障排除提示”，包括问题和解决方案 4。我可以选择相关的“类别”并添加“标签”4。当我提交表单时，服务器进入待审核状态。我收到确认消息。US-007: 编辑我提交的 MCP 服务器描述：作为一名开发者，我想要编辑我之前提交的 MCP 服务器的详细信息，以便我能保持其信息最新。验收标准：当我以已提交服务器的作者身份登录时。当我导航到我的“我的服务器”仪表板时。我选择一个现有服务器进行编辑。我看到提交表单预填充了服务器的当前数据 4。我可以修改任何字段并重新提交以供审核。当我重新提交时，服务器的“上次更新”时间戳在批准后更新 3。6.4. 社区与反馈用户故事US-008: 提供有用性反馈描述：作为一名用户，我想要快速反馈页面是否有用，以便平台可以改进其内容。验收标准：当我位于任何服务器列表或详情页面时。当我点击“这有用吗？”提示中的“是”或“否”时 3。我的反馈被记录。反馈提示消失或改变以表明我的提交。US-009: 为 MCP 服务器留下评论描述：作为一名用户，我想要为 MCP 服务器留下评论和评分，以便我能分享我的经验并帮助其他用户。验收标准：当我登录并位于 MCP 服务器的详情页面时。当我导航到“评论”部分时 4。我可以看到现有评论和一个提交新评论的表单。我可以选择一个评分（例如，1-5 星）并输入评论。当我提交评论时，它在审核后（如果适用）出现在页面上。服务器的整体评分更新 4。表：用户故事目录下表提供了结构化、可操作的开发任务列表，直接映射到功能需求。其详细的验收标准旨在足够精确，以便 AI Agent 能够解释并生成相关的代码，涵盖前端 UI 和后端逻辑。这种详细程度确保了开发过程的完整性并减少了歧义。故事 ID标题描述验收标准优先级US-001查看所有 MCP 服务器作为一名用户，我想要查看所有可用的 MCP 服务器列表，以便我能探索所提供的功能范围。当我导航到服务器列表页面 (/mcp/servers) 时，我看到“开源 MCP 服务器”的总数和“上次更新”时间戳。我看到一个分页/可滚动的 MCP 服务器条目列表，每个条目显示名称、官方标签、开发者、质量指标、描述、上次更新日期、编程语言、许可证类型和兼容的操作系统图标。当我滚动到列表底部时，出现“加载更多”按钮或自动加载新条目。高US-002按类别筛选服务器作为一名用户，我想要按特定类别筛选服务器列表，以便我能找到与我感兴趣领域相关的服务器。当我位于服务器列表页面时，我从侧边栏/筛选选项中选择一个类别（例如，“Python”）。URL 更新以反映该类别，显示的列表仅显示标记为该类别的服务器，且类别计数准确。每个显示的服务器条目都符合 US-001 的标准。高US-003按受欢迎程度/新近度排序服务器作为一名用户，我想要按各种标准（如下载量或最近更新）对服务器列表进行排序，以便我能优先查看热门或最新活跃的服务器。当我位于服务器列表页面时，我从排序下拉菜单中选择“每周下载量”或“更新日期”。服务器列表重新排序，以最高每周下载量或最近更新的服务器优先显示。排序选项包括：搜索相关性、添加日期、更新日期、每周下载量、GitHub 星数、最近 GitHub 星数。高US-004按关键词搜索服务器作为一名用户，我想要使用关键词搜索服务器，以便我能快速找到特定的服务器或功能。当我位于服务器列表页面时，我在搜索栏中输入关键词并按 Enter 键。页面显示名称、描述或标签中包含关键词的服务器列表。“深度搜索”和“新搜索”按钮可用。每个显示的服务器条目都符合 US-001 的标准。高US-005查看单个服务器详情作为一名用户，我想要查看特定 MCP 服务器的综合详情页面，以便我能了解其功能、设置和集成说明。当我点击服务器列表中的一个服务器条目时，我被导航到服务器的详情页面。页面突出显示服务器标题、作者、标签、许可证、兼容性，并包含导航链接、开发链接、详细的功能、集成、设置、安装、故障排除、工具、质量指标、安装按钮、HTTP 连接 URL、相关/新服务器列表和有用性反馈按钮。高US-006提交新的 MCP 服务器作为一名开发者，我想要向市场提交一个新的 MCP 服务器，以便它能被他人发现和使用。当我以开发者身份登录并点击“添加服务器”按钮时，我看到一个多步骤表单，用于输入服务器的详细信息。表单包含所有必要的字段，包括结构化的功能、工具、集成步骤和故障排除提示。提交后，服务器进入待审核状态，并收到确认消息。中US-007编辑我提交的 MCP 服务器作为一名开发者，我想要编辑我之前提交的 MCP 服务器的详细信息，以便我能保持其信息最新。当我以已提交服务器的作者身份登录并导航到“我的服务器”仪表板时，我选择一个现有服务器进行编辑。我看到提交表单预填充了服务器的当前数据，可以修改任何字段并重新提交以供审核。重新提交后，服务器的“上次更新”时间戳在批准后更新。中US-008提供有用性反馈作为一名用户，我想要快速反馈页面是否有用，以便平台可以改进其内容。当我位于任何服务器列表或详情页面时，我点击“这有用吗？”提示中的“是”或“否”。我的反馈被记录，反馈提示消失或改变以表明我的提交。低US-009为 MCP 服务器留下评论作为一名用户，我想要为 MCP 服务器留下评论和评分，以便我能分享我的经验并帮助其他用户。当我登录并位于 MCP 服务器的详情页面时，我导航到“评论”部分。我可以看到现有评论和一个提交新评论的表单。我可以选择一个评分并输入评论。提交后，评论在审核后（如果适用）出现在页面上，服务器的整体评分更新。低7. 结论与下一步7.1. 拟议架构与开发方法总结本报告概述了一个基于领域驱动设计的健壮、模块化架构，强调通过边界上下文实现清晰的关注点分离。这种分解有助于避免单体设计，降低开发复杂性，并允许系统不同部分独立演进和扩展。详细的功能需求和敏捷用户故事为迭代开发提供了清晰的路线图，由于其特异性和完整性，非常适合利用 AI Agent 进行代码生成。这种方法将确保开发过程的高效性和产出的高质量。7.2. 实施与未来增强建议为了成功实施和持续发展 MCP 服务器市场，建议采取以下步骤和考虑未来的增强功能：分阶段开发：建议优先开发核心服务器列表和发现功能（US-001 至 US-005），其次是服务器提交和管理功能（US-006，US-007），最后是社区功能（US-008，US-009）。这种迭代方法将确保核心价值的快速交付和用户反馈的早期整合。API 优先方法：开发一个全面的市场 API（如 4 中“MCP 目录 API”所暗示），以实现 AI Agent 和其他应用程序的编程访问，而不仅仅是基于 Web 的用户界面。这将极大地扩展平台的实用性和集成潜力。自动化质量检查：探索集成自动化工具，在服务器提交过程中生成/验证 Asecurity、Alicense 和 Aquality 评分。这将提高数据质量和用户对服务器信息的信任。AI 驱动的搜索：研究使用 LLM 增强市场自身的搜索能力，允许用户使用自然语言查询来查找相关的 MCP 服务器。这将提升用户体验并进一步利用 AI 技术。监控与分析：实施强大的日志记录和分析系统，以跟踪服务器使用情况、热门类别和用户行为，从而为未来的开发提供信息并优化平台。可扩展性考虑：在设计数据模型和服务时，应充分考虑可扩展性，以应对服务器数量和用户流量的增长。确保基础设施能够支持未来的扩展需求。