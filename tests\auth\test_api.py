from fastapi.testclient import Test<PERSON>lient


def should_return_access_token_when_valid_credentials_provided(
    test_client: TestClient, auth_credentials: dict[str, str]
) -> None:
    """Test that access token is returned when valid credentials are provided"""
    response = test_client.post(
        "/api/v1/auth/login",
        json={
            "username": auth_credentials["username"],
            "password": auth_credentials["password"],
        },
    )
    assert response.status_code == 200
    assert "access_token" in response.json()


def should_deny_access_when_invalid_credentials_provided(
    test_client: TestClient,
) -> None:
    """Test that access is denied when invalid credentials are provided"""
    response = test_client.post(
        "/api/v1/auth/login",
        json={"username": "wronguser", "password": "wrongpassword"},
    )
    assert response.status_code == 401
    assert "detail" in response.json()


def should_allow_access_to_protected_route_with_valid_token(
    test_client: TestClient, auth_credentials: dict[str, str]
) -> None:
    """Test that protected route is accessible with valid token"""
    # Get valid token first
    login_response = test_client.post(
        "/api/v1/auth/login",
        json={
            "username": auth_credentials["username"],
            "password": auth_credentials["password"],
        },
    )
    token = login_response.json()["access_token"]

    # Test protected route
    response = test_client.get(
        "/api/v1/auth/protected", headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    assert "message" in response.json()
