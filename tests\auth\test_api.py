import pytest
from unittest.mock import patch, AsyncMock
from fastapi.testclient import Test<PERSON><PERSON>


def should_return_access_token_when_valid_credentials_provided(
    test_client: TestClient, auth_credentials: dict[str, str]
) -> None:
    """Test that access token is returned when valid credentials are provided"""
    response = test_client.post(
        "/api/v1/auth/login",
        json={
            "username": auth_credentials["username"],
            "password": auth_credentials["password"],
        },
    )
    assert response.status_code == 200
    assert "access_token" in response.json()


def should_deny_access_when_invalid_credentials_provided(
    test_client: TestClient,
) -> None:
    """Test that access is denied when invalid credentials are provided"""
    response = test_client.post(
        "/api/v1/auth/login",
        json={"username": "wronguser", "password": "wrongpassword"},
    )
    assert response.status_code == 401
    assert "detail" in response.json()


def should_allow_access_to_protected_route_with_valid_token(
    test_client: TestClient, auth_credentials: dict[str, str]
) -> None:
    """Test that protected route is accessible with valid token"""
    # Get valid token first
    login_response = test_client.post(
        "/api/v1/auth/login",
        json={
            "username": auth_credentials["username"],
            "password": auth_credentials["password"],
        },
    )
    token = login_response.json()["access_token"]

    # Test protected route
    response = test_client.get(
        "/api/v1/auth/protected", headers={"Authorization": f"Bearer {token}"}
    )
    assert response.status_code == 200
    assert "message" in response.json()


# OAuth API Tests


def should_generate_github_authorization_url_when_valid_provider_requested(
    test_client: TestClient,
) -> None:
    """Test that GitHub authorization URL is generated successfully"""
    response = test_client.get("/api/v1/auth/oauth/authorize/github")

    assert response.status_code == 200
    data = response.json()
    assert "authorization_url" in data
    assert "provider" in data
    assert data["provider"] == "github"
    assert "https://github.com/login/oauth/authorize" in data["authorization_url"]


def should_generate_google_authorization_url_when_valid_provider_requested(
    test_client: TestClient,
) -> None:
    """Test that Google authorization URL is generated successfully"""
    response = test_client.get("/api/v1/auth/oauth/authorize/google")

    assert response.status_code == 200
    data = response.json()
    assert "authorization_url" in data
    assert "provider" in data
    assert data["provider"] == "google"
    assert "https://accounts.google.com/o/oauth2/v2/auth" in data["authorization_url"]


def should_include_state_parameter_when_state_provided_in_authorization_url(
    test_client: TestClient,
) -> None:
    """Test that state parameter is included in authorization URL when provided"""
    state = "test_state_123"
    response = test_client.get(f"/api/v1/auth/oauth/authorize/github?state={state}")

    assert response.status_code == 200
    data = response.json()
    assert data["state"] == state
    assert f"state={state}" in data["authorization_url"]


def should_reject_invalid_oauth_provider_when_unsupported_provider_requested(
    test_client: TestClient,
) -> None:
    """Test that invalid OAuth provider is rejected"""
    response = test_client.get("/api/v1/auth/oauth/authorize/invalid")

    assert response.status_code == 422  # Validation error due to regex constraint


@patch("modules.auth.application.oauth_client_service.OAuthClientService.handle_oauth_flow")
def should_handle_github_oauth_callback_when_valid_code_provided(
    mock_handle_oauth_flow: AsyncMock, test_client: TestClient
) -> None:
    """Test that GitHub OAuth callback is handled successfully"""
    # Mock the OAuth flow to return user info and token
    mock_handle_oauth_flow.return_value = {
        "user_info": {
            "oauth_id": "12345",
            "email": "<EMAIL>",
            "name": "Test User",
            "username": "testuser",
        },
        "token_data": {"access_token": "test_token", "token_type": "bearer"},
    }

    # Mock the auth service to return a user and token
    with patch("modules.auth.application.services.AuthService.handle_oauth_flow") as mock_auth_flow:
        mock_auth_flow.return_value = (None, "jwt_access_token")

        response = test_client.post(
            "/api/v1/auth/oauth/github/callback",
            json={"code": "test_authorization_code"},
        )

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"


@patch("modules.auth.application.oauth_client_service.OAuthClientService.handle_oauth_flow")
def should_handle_google_oauth_callback_when_valid_code_provided(
    mock_handle_oauth_flow: AsyncMock, test_client: TestClient
) -> None:
    """Test that Google OAuth callback is handled successfully"""
    # Mock the OAuth flow to return user info and token
    mock_handle_oauth_flow.return_value = {
        "user_info": {
            "oauth_id": "67890",
            "email": "<EMAIL>",
            "name": "Test User",
            "username": "test",
        },
        "token_data": {"access_token": "test_token", "token_type": "bearer"},
    }

    # Mock the auth service to return a user and token
    with patch("modules.auth.application.services.AuthService.handle_oauth_flow") as mock_auth_flow:
        mock_auth_flow.return_value = (None, "jwt_access_token")

        response = test_client.post(
            "/api/v1/auth/oauth/google/callback",
            json={"code": "test_authorization_code", "state": "test_state"},
        )

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"


def should_reject_oauth_callback_when_invalid_code_provided(
    test_client: TestClient,
) -> None:
    """Test that OAuth callback is rejected when invalid code is provided"""
    with patch("modules.auth.application.services.AuthService.handle_oauth_flow") as mock_auth_flow:
        mock_auth_flow.side_effect = ValueError("Invalid authorization code")

        response = test_client.post(
            "/api/v1/auth/oauth/github/callback",
            json={"code": "invalid_code"},
        )

        assert response.status_code == 400
        assert "Invalid OAuth data" in response.json()["detail"]
