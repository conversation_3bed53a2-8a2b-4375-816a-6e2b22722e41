import os
from collections.abc import Generator
from functools import lru_cache

from pydantic_settings import BaseSettings
from sqlalchemy.orm import Session

from common.db.database import SessionLocal


# 基础配置模型
class AppConfig(BaseSettings):
    jwt_secret: str = os.getenv("AI4SE_MCP_HUB_JWT_SECRET", "default-secret")
    jwt_algorithm: str = "HS256"
    token_expire_minutes: int = 30


# 公共依赖项
@lru_cache
def get_config() -> AppConfig:
    return AppConfig()


def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
